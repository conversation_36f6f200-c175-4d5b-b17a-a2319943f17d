/* ===== MODERN ANIMATIONS & EFFECTS ===== */

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Animation Classes - DISABLED to prevent jitter */
.animate-fade-in-up {
    /* animation: fadeInUp 0.6s ease-out; */ /* Disabled */
}

.animate-fade-in-left {
    /* animation: fadeInLeft 0.6s ease-out; */ /* Disabled */
}

.animate-fade-in-right {
    /* animation: fadeInRight 0.6s ease-out; */ /* Disabled */
}

.animate-pulse {
    /* animation: pulse 2s infinite; */ /* Disabled */
}

.animate-bounce {
    /* animation: bounce 1s infinite; */ /* Disabled */
}

.animate-slide-in-top {
    /* animation: slideInFromTop 0.5s ease-out; */ /* Disabled */
}

/* Hover Effects - DISABLED to prevent jitter */
.hover-lift {
    /* transition: all 0.3s ease; */ /* Disabled */
}

.hover-lift:hover {
    /* transform: translateY(-8px); */ /* Disabled */
    /* box-shadow: 0 12px 24px rgba(46, 139, 139, 0.2); */ /* Disabled */
}

.hover-scale {
    /* transition: transform 0.3s ease; */ /* Disabled */
}

.hover-scale:hover {
    /* transform: scale(1.05); */ /* Disabled */
}

.hover-glow {
    /* transition: all 0.3s ease; */ /* Disabled */
}

.hover-glow:hover {
    /* box-shadow: 0 0 20px rgba(46, 139, 139, 0.4); */ /* Disabled */
}

/* Loading Animations - DISABLED */
.loading-shimmer {
    background: #f0f0f0; /* Static background */
    /* background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite; */ /* DISABLED */
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2E8B8B;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    /* animation: spin 1s linear infinite; */ /* DISABLED */
}

@keyframes spin {
    /* 0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); } */ /* DISABLED */
}

/* Stagger Animation for Lists - DISABLED to prevent jitter */
.stagger-animation > * {
    opacity: 1; /* Always visible */
    transform: translateY(0); /* No transform */
    /* animation: fadeInUp 0.6s ease-out forwards; */ /* Disabled */
}

/* All animation delays disabled */
.stagger-animation > *:nth-child(1) { /* animation-delay: 0.1s; */ }
.stagger-animation > *:nth-child(2) { /* animation-delay: 0.2s; */ }
.stagger-animation > *:nth-child(3) { /* animation-delay: 0.3s; */ }
.stagger-animation > *:nth-child(4) { /* animation-delay: 0.4s; */ }
.stagger-animation > *:nth-child(5) { /* animation-delay: 0.5s; */ }
.stagger-animation > *:nth-child(6) { /* animation-delay: 0.6s; */ }

/* Smooth Transitions - DISABLED */
.smooth-transition {
    /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ /* DISABLED */
}

/* Glass Morphism Effect */
.glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #2E8B8B 0%, #7FB069 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Modern Button Hover Effects - DISABLED */
.btn-modern {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-modern::before {
    /* content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
    z-index: -1; */ /* DISABLED */
}

.btn-modern:hover::before {
    /* left: 100%; */ /* DISABLED */
}

/* Floating Animation - DISABLED */
.floating {
    /* animation: floating 3s ease-in-out infinite; */ /* DISABLED */
}

@keyframes floating {
    /* 0% { transform: translate(0, 0px); }
    50% { transform: translate(0, -10px); }
    100% { transform: translate(0, 0px); } */ /* DISABLED */
}

/* Typewriter Effect - DISABLED */
.typewriter {
    overflow: hidden;
    /* border-right: .15em solid #2E8B8B; */ /* DISABLED */
    white-space: nowrap;
    margin: 0 auto;
    letter-spacing: .15em;
    /* animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite; */ /* DISABLED */
}

@keyframes typing {
    /* from { width: 0 }
    to { width: 100% } */ /* DISABLED */
}

@keyframes blink-caret {
    /* from, to { border-color: transparent }
    50% { border-color: #2E8B8B; } */ /* DISABLED */
}

/* Parallax Effect - DISABLED */
.parallax {
    /* transform: translateZ(0); */ /* DISABLED */
    /* transition: transform 0.1s ease-out; */ /* DISABLED */
}

/* Scroll Reveal Animation - DISABLED to prevent jitter */
.scroll-reveal {
    opacity: 1; /* Always visible */
    transform: translateY(0); /* No transform */
    /* transition: all 0.6s ease; */ /* Disabled */
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Modern Card Flip Effect - DISABLED */
.flip-card {
    background-color: transparent;
    /* perspective: 1000px; */ /* DISABLED */
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    /* transition: transform 0.6s; */ /* DISABLED */
    /* transform-style: preserve-3d; */ /* DISABLED */
}

.flip-card:hover .flip-card-inner {
    /* transform: rotateY(180deg); */ /* DISABLED */
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 1rem;
}

.flip-card-back {
    /* transform: rotateY(180deg); */ /* DISABLED */
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* GLOBAL ANIMATION DISABLE - Prevent all jitter */
* {
    /* Disable problematic transitions that cause jitter */
    transition: none !important;
    animation: none !important;
}

/* FORCE DISABLE scroll-reveal animations on cards and tables */
.card.scroll-reveal,
.card.scroll-reveal.revealed,
.table-responsive.scroll-reveal,
.table-responsive.scroll-reveal.revealed,
div[class*="scroll-reveal"] {
    animation: none !important;
    animation-delay: 0s !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 1 !important;
    transition: none !important;
}

/* Override inline styles for scroll-reveal elements */
.scroll-reveal[style*="animation-delay"],
.scroll-reveal[style*="transform"],
.scroll-reveal[style*="box-shadow"] {
    animation: none !important;
    animation-delay: 0s !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 1 !important;
}

/* Re-enable only essential transitions */
.btn, .form-control, .nav-link {
    transition: background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out,
                color 0.15s ease-in-out !important;
}

/* Keep smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* ULTIMATE ANIMATION KILLER - Override everything */
*[style*="animation"],
*[style*="transform"],
*[style*="transition"],
*[class*="scroll-reveal"],
*[class*="revealed"],
.card[style],
.table-responsive[style],
div[style*="animation-delay"],
div[style*="box-shadow"] {
    animation: none !important;
    animation-delay: 0s !important;
    animation-duration: 0s !important;
    animation-iteration-count: 0 !important;
    transform: none !important;
    transition: none !important;
    box-shadow: none !important;
    opacity: 1 !important;
}

/* Force remove specific problematic styles */
*[style*="rgba(46, 139, 139"] {
    box-shadow: none !important;
}

*[style*="translateY"] {
    transform: none !important;
}

*[style*="scale"] {
    transform: none !important;
}
