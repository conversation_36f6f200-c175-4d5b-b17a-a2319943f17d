/* ===== MODERN ADMIN DASHBOARD STYLES ===== */

/* Import color variables from main styles */
:root {
    /* Admin-specific colors */
    --admin-primary: #2E8B8B;
    --admin-primary-dark: #1F5F5F;
    --admin-primary-light: #4A9F9F;
    --admin-secondary: #4A90A4;
    --admin-accent: #7FB069;
    --admin-bg: #F8FFFE;
    --admin-bg-secondary: #F0F8F8;
    --admin-card-bg: #FFFFFF;
    --admin-sidebar-bg: #FAFCFC;
    --admin-text: #2C3E50;
    --admin-text-secondary: #5A6C7D;
    --admin-text-muted: #8B9DC3;
    --admin-border: #E1E8ED;
    --admin-shadow: 0 4px 12px rgba(46, 139, 139, 0.12);
    --admin-shadow-lg: 0 8px 24px rgba(46, 139, 139, 0.16);
}

/* Container and Layout */
.container-fluid {
    background: linear-gradient(135deg, var(--admin-bg) 0%, var(--admin-bg-secondary) 100%);
    min-height: 100vh;
    padding: 2rem 1.5rem;
}

/* Modern Card Styles */
.card {
    background-color: var(--admin-card-bg);
    box-shadow: var(--admin-shadow);
    margin-bottom: 2rem;
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-accent) 100%);
}

/* Card hover effect disabled to prevent jitter */
.card:hover {
    /* transform: translateY(-4px); */ /* Disabled */
    /* box-shadow: var(--admin-shadow-lg); */ /* Disabled */
}

.card-body {
    padding: 2rem;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
    background: linear-gradient(180deg, var(--admin-sidebar-bg) 0%, var(--admin-bg-secondary) 100%);
    border-right: 3px solid var(--admin-primary);
    box-shadow: 4px 0 12px rgba(46, 139, 139, 0.08);
    position: fixed;
    top: 85px;
    left: 0;
    width: 16.66667%; /* col-md-3 equivalent */
    height: calc(100vh - 185px); /* Subtract navbar height (85px) and footer height (100px) */
    z-index: 999; /* Lower than footer to prevent overlap */
    overflow-y: auto;
    overflow-x: hidden;
}

/* Adjust main content to account for fixed sidebar */
.col-md-9.ms-sm-auto.col-lg-10 {
    margin-left: 16.66667% !important; /* Match sidebar width */
    padding-bottom: 120px; /* Add space for footer */
    min-height: calc(100vh - 85px); /* Ensure content area is tall enough */
}

/* Ensure footer is always visible and not covered by sidebar */
.modern-footer {
    position: relative !important;
    z-index: 1001 !important;
    clear: both;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        height: auto;
        width: 100%;
        margin-left: 0;
        top: auto;
        left: auto;
        z-index: auto;
    }

    .col-md-9.ms-sm-auto.col-lg-10 {
        margin-left: 0 !important;
        padding-bottom: 20px; /* Reduce padding on mobile */
    }
}

.sidebar .nav-link {
    color: var(--admin-text);
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    margin: 0.25rem 0.75rem;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

/* Disabled sweep animation to prevent jitter */
.sidebar .nav-link::before {
    /* content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(46, 139, 139, 0.1), transparent);
    transition: left 0.5s; */
}

.sidebar .nav-link:hover::before {
    /* left: 100%; */
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
    color: white;
    /* transform: translateX(8px); */ /* Disabled to prevent jitter */
    box-shadow: 0 4px 12px rgba(46, 139, 139, 0.3);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-accent) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 139, 139, 0.3);
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* ===== HEADER STYLES ===== */
.border-bottom {
    border-bottom: 3px solid var(--admin-border) !important;
    padding-bottom: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
}

.border-bottom::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-accent) 100%);
    border-radius: 2px;
}

.h2 {
    color: var(--admin-text);
    font-weight: 700;
    margin: 0;
    font-size: 2rem;
    position: relative;
}

.h2::before {
    
    margin-right: 0.75rem;
    font-size: 1.5rem;
}

/* Style cho bảng */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 2px solid #dee2e6;
    color: #2c3e50;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Style cho nút thao tác */
.btn-group {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-group .btn i {
    font-size: 0.875rem;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Style cho badge */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Style cho alert */
.alert {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Style cho modal */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Style cho DataTables */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_info {
    padding-top: 1rem;
    color: #6c757d;
}

.dataTables_wrapper .dataTables_paginate {
    padding-top: 1rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0.25rem;
    margin: 0 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #0d6efd;
    border-color: #0d6efd;
    color: #fff !important;
}

/* Căn giữa pagination cho tất cả các bảng DataTables */
.dataTables_wrapper .dataTables_paginate {
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    padding-top: 1rem;
}

.dataTables_wrapper .dataTables_paginate .pagination {
    margin: 0 auto;
    justify-content: center;
}

/* Đảm bảo pagination luôn căn giữa cho tất cả các table ID */
#patient-table_paginate,
#appointmentTable_paginate,
#patientTable_paginate,
#userTable_paginate,
#serviceTable_paginate {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
}

#patient-table_paginate .pagination,
#appointmentTable_paginate .pagination,
#patientTable_paginate .pagination,
#userTable_paginate .pagination,
#serviceTable_paginate .pagination {
    margin: 0 auto;
    justify-content: center;
}

/* ===== UNIFIED TABLE STYLES - Áp dụng cho tất cả bảng ===== */

/* Style chung cho container và card */
.container-fluid {
    background-color: #f8f9fa;
    min-height: 10vh;
}

.card {
    background-color: #ffffff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.card-body {
    padding: 1.5rem;
}

/* Style cho bảng - Áp dụng cho tất cả table */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    text-align: left;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
    transition: background-color 0.15s ease-in-out;
}

/* Style cho nút thao tác */
.btn-group {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.btn-group .btn i {
    font-size: 0.875rem;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Style cho badge */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

/* Style cho alert */
.alert {
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    border: 1px solid transparent;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

/* Style cho modal */
.modal-content {
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.175);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Style cho DataTables */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_info {
    padding-top: 1rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_length select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

/* Style cho table responsive */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

/* Style cho header và toolbar */
.d-flex.justify-content-between {
    align-items: center;
}

.h2 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0;
}

.btn-toolbar .btn {
    margin-left: 0.5rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.btn-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000;
}

.btn-info:hover {
    background-color: #31d2f2;
    border-color: #25cff2;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
}

/* ===== MODERN DASHBOARD STATISTICS CARDS ===== */

/* Statistics Card Container */
.statistics-card {
    background: linear-gradient(135deg, var(--admin-card-bg) 0%, var(--admin-bg-secondary) 100%);
    border-radius: 1.25rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--admin-shadow);
}

.statistics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-accent) 100%);
}

.statistics-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--admin-shadow-lg);
}

/* Border left styles for statistics cards - Modern approach */
.border-left-primary {
    border-left: 6px solid var(--admin-primary) !important;
    background: linear-gradient(135deg, rgba(46, 139, 139, 0.05) 0%, rgba(46, 139, 139, 0.02) 100%);
}

.border-left-success {
    border-left: 6px solid #27AE60 !important;
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.05) 0%, rgba(39, 174, 96, 0.02) 100%);
}

.border-left-info {
    border-left: 6px solid #3498DB !important;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(52, 152, 219, 0.02) 100%);
}

.border-left-warning {
    border-left: 6px solid #F39C12 !important;
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.05) 0%, rgba(243, 156, 18, 0.02) 100%);
}

.border-left-danger {
    border-left: 6px solid #E74C3C !important;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(231, 76, 60, 0.02) 100%);
}

/* Icon styling for statistics cards */
.statistics-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 139, 139, 0.3);
}

/* Text colors for statistics */
.text-gray-300 {
    color: var(--admin-text-muted) !important;
}

.text-gray-800 {
    color: var(--admin-text) !important;
}

.text-primary {
    color: var(--admin-primary) !important;
}

.text-success {
    color: #27AE60 !important;
}

.text-info {
    color: #3498DB !important;
}

.text-warning {
    color: #F39C12 !important;
}

.text-danger {
    color: #E74C3C !important;
}

/* Font sizes and weights */
.text-xs {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--admin-text-secondary);
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* Statistics number styling */
.statistics-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--admin-text);
    margin: 0.5rem 0;
    text-shadow: 0 2px 4px rgba(46, 139, 139, 0.1);
}

/* No gutters utility */
.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

/* Height utilities */
.h-100 {
    height: 100% !important;
}

/* Padding utilities */
.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

.mr-2 {
    margin-right: 0.5rem !important;
}

.mb-1 {
    margin-bottom: 0.25rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

/* Statistics card hover effect */
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
}

/* ===== MODERN TABLE STYLES ===== */
.modern-table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: none;
}

.modern-table thead th {
    background: linear-gradient(135deg, var(--admin-bg-secondary) 0%, var(--admin-card-bg) 100%);
    border: none;
    font-weight: 600;
    color: var(--admin-text);
    padding: 1rem 1.25rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table tbody td {
    padding: 1rem 1.25rem;
    border: none;
    border-bottom: 1px solid var(--admin-border);
    vertical-align: middle;
}

.table-row-hover {
    transition: background-color 0.2s ease;
}

.table-row-hover:hover {
    background: linear-gradient(135deg, rgba(46, 139, 139, 0.03) 0%, rgba(46, 139, 139, 0.01) 100%);
    /* Removed transform and box-shadow for subtle effect */
}

/* Activity Table Specific Styles */
.activity-time-badge {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-align: center;
    min-width: 50px;
}

.activity-description {
    font-weight: 500;
    color: var(--admin-text);
    line-height: 1.4;
}

.user-avatar {
    color: var(--admin-primary);
    font-size: 1.25rem;
}

.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.5;
}

/* Badge Gradients */
.bg-gradient {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%) !important;
}

.bg-gradient.bg-info {
    background: linear-gradient(135deg, #3498DB 0%, #5DADE2 100%) !important;
}

.bg-gradient.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #8d9498 100%) !important;
}

.bg-gradient.bg-success {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%) !important;
}

.bg-gradient.bg-warning {
    background: linear-gradient(135deg, #F39C12 0%, #F1C40F 100%) !important;
}

.bg-gradient.bg-danger {
    background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%) !important;
}

/* Statistics Icon Background Colors */
.statistics-icon.bg-success {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%) !important;
}

.statistics-icon.bg-info {
    background: linear-gradient(135deg, #3498DB 0%, #5DADE2 100%) !important;
}

.statistics-icon.bg-warning {
    background: linear-gradient(135deg, #F39C12 0%, #F1C40F 100%) !important;
}

.statistics-icon.bg-danger {
    background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%) !important;
}

/* Style cho responsive */
@media screen and (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-group .btn {
        width: 100%;
    }

    .table-responsive {
        margin: 0 -1rem;
    }

    /* Statistics cards responsive */
    .col-xl-3 {
        margin-bottom: 1rem;
    }

    .text-xs {
        font-size: 0.65rem;
    }

    /* Modern table responsive */
    .modern-table thead th {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .modern-table tbody td {
        padding: 0.75rem 1rem;
    }

    .activity-time-badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        min-width: 40px;
    }

    .table-row-hover:hover {
        background: rgba(46, 139, 139, 0.02);
        /* Keep subtle background change on mobile */
    }

    .empty-state {
        padding: 2rem 1rem;
    }
}

/* ===== TIMELINE STYLES ===== */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, var(--admin-primary) 0%, var(--admin-accent) 100%);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -1.75rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-content {
    background: var(--admin-bg-secondary);
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.timeline-title {
    margin-bottom: 0.5rem;
    color: var(--admin-text);
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 0;
    color: var(--admin-text-secondary);
    font-size: 0.875rem;
}

/* List Group Modern Styles */
.list-group-item {
    border: none;
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 0;
    background: transparent;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background: var(--admin-bg-secondary);
    padding-left: 1rem;
    border-radius: 0.5rem;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Badge Improvements */
.badge.rounded-pill {
    padding: 0.5em 1em;
    font-weight: 500;
    font-size: 0.75rem;
}

/* Responsive Timeline */
@media screen and (max-width: 768px) {
    .timeline {
        padding-left: 1.5rem;
    }

    .timeline::before {
        left: 0.5rem;
    }

    .timeline-marker {
        left: -1.5rem;
        width: 0.75rem;
        height: 0.75rem;
    }

    .timeline-item {
        padding-left: 1rem;
    }

    .timeline-content {
        padding: 0.75rem;
    }
}