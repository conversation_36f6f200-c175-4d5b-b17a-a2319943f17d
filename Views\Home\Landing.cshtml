@{
    ViewData["Title"] = "<PERSON><PERSON>o mừng đến với <PERSON> thống <PERSON> khoa";
    Layout = "_Layout";
}

@section Styles {
    <style>
        .landing-container {
            background: linear-gradient(135deg, #4A9F9F 0%, #6BB6B6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        
        .landing-content {
            text-align: center;
            color: white;
            max-width: 800px;
            padding: 0 20px;
        }
        
        .landing-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .landing-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .landing-buttons {
            margin-top: 40px;
        }
        
        .btn-landing {
            background: white;
            color: #4A9F9F;
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            margin: 10px;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-landing:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            color: #2E7D7D;
        }
        
        .btn-landing.btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn-landing.btn-outline:hover {
            background: white;
            color: #4A9F9F;
        }
        
        .features {
            margin-top: 60px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: white;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .feature-description {
            opacity: 0.9;
            line-height: 1.5;
        }
        
        @@media (max-width: 768px) {
            .landing-title {
                font-size: 2.5rem;
            }

            .landing-subtitle {
                font-size: 1.1rem;
            }

            .btn-landing {
                display: block;
                margin: 10px auto;
                max-width: 250px;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
}

<div class="landing-container">
    <div class="landing-content">
        <h1 class="landing-title">
            Chào mừng đến với<br>
            Hệ thống Nha khoa
        </h1>
        
        <p class="landing-subtitle">
            Hệ thống quản lý nha khoa hiện đại, giúp bạn dễ dàng đặt lịch hẹn, 
            theo dõi điều trị và quản lý thông tin sức khỏe răng miệng của mình.
        </p>
        
        <div class="landing-buttons">
            <a href="@Url.Action("UserRegister", "Account")" class="btn-landing">
                Đăng ký ngay
            </a>
            <a href="@Url.Action("Login", "Account")" class="btn-landing btn-outline">
                Đăng nhập
            </a>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h3 class="feature-title">Đặt lịch hẹn</h3>
                <p class="feature-description">
                    Dễ dàng đặt lịch hẹn khám và theo dõi lịch trình điều trị của bạn
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <h3 class="feature-title">Bác sĩ chuyên nghiệp</h3>
                <p class="feature-description">
                    Đội ngũ bác sĩ giàu kinh nghiệm và trang thiết bị hiện đại
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h3 class="feature-title">Quản lý hóa đơn</h3>
                <p class="feature-description">
                    Theo dõi chi phí điều trị và thanh toán trực tuyến tiện lợi
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add fade-in animation
            $('.landing-content').hide().fadeIn(1000);
            
            // Stagger animation for feature cards
            $('.feature-card').each(function(index) {
                $(this).delay(index * 200).fadeIn(800);
            });
            
            // Smooth scroll for buttons
            $('.btn-landing').on('click', function(e) {
                $(this).addClass('animate__pulse');
                setTimeout(() => {
                    $(this).removeClass('animate__pulse');
                }, 600);
            });
        });
    </script>
}
